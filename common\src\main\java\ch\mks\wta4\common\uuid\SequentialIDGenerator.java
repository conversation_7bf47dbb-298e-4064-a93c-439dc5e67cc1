package ch.mks.wta4.common.uuid;

import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;

public class SequentialIDGenerator extends AbstractWTA4Service{

    private final static Logger LOG = LoggerFactory.getLogger(SequentialIDGenerator.class);

    private static final String REFERENCE_DATE_KEY = "REFERENCE_DATE";
    private final String storeFile;
    private Properties properties;
    private final Map<UUIDPrefix, AtomicInteger> counters;
    private Clock clock;
    private LocalDate referenceDate;
    private final ZoneId zoneId;
    private final DateTimeFormatter localDateFormatter;
    private final Object lockSaveToStore = new Object();
    private final ExecutorService saveToStoreExecutor;
    private final String instanceUniqueId;
    

    public SequentialIDGenerator(String storeFile, ZoneId zoneId, String instanceUniqueId) {
        this.storeFile = storeFile;
        this.zoneId = zoneId;
        this.instanceUniqueId = instanceUniqueId.toUpperCase();
        this.counters = new ConcurrentHashMap<>();
        this.clock = Clock.system(zoneId);
        properties = new Properties();
        localDateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        saveToStoreExecutor = Executors.newSingleThreadExecutor(ThreadUtils.getThreadFactory("wta4-sequential-id-generator-store-synch"));
    }
    
    public void setClockONLYForTesting(Clock clock) {
        this.clock = clock;
    }

    @Override
    protected void startUp() throws Exception {
        LOG.info("startUp -> storeFile={}", storeFile);
        
        Path path = Paths.get(storeFile);

        if ( ! Files.exists(path) ) {
            
            LOG.info("startup - {} not found, creating it", storeFile);
            Files.createDirectories(path.getParent());
            Files.createFile(path);
            referenceDate = getLocalDateNow();
            synchSaveToStore();
            
        } else {
            FileReader fr = new FileReader(storeFile);
            properties.load(fr);
            fr.close();
            referenceDate = getStoredDate();
            LOG.info("startUp - properties={}", properties);
        }
                
        LOG.info("startUp <-");
    }

    private LocalDate getLocalDateNow() {
        Instant now = clock.instant();
        return now.atZone(zoneId).toLocalDate();
    }

    private LocalDate getStoredDate() {
        String dateStr = properties.getProperty(REFERENCE_DATE_KEY);
        return LocalDate.parse(dateStr);
    }

    @Override
    protected void shutDown() throws Exception {
        synchSaveToStore();
        saveToStoreExecutor.shutdown();
        saveToStoreExecutor.awaitTermination(5, TimeUnit.SECONDS);
        LOG.info("shutDown - properties={}", properties);
    }
    
    private void asynchSaveToStore() {
        saveToStoreExecutor.submit(() -> synchSaveToStore());
    }

    private void synchSaveToStore() {
        synchronized (lockSaveToStore) {
            try (FileWriter fw = new FileWriter(storeFile)) {
                properties.setProperty(REFERENCE_DATE_KEY, referenceDate.toString());
                counters.forEach((key,value) -> properties.setProperty(key.toString(), Integer.valueOf(value.get()).toString()));
                properties.store(fw, "");
            } catch (Exception e) {
                LOG.error("synchWithStore - store={}", storeFile, e);
            }    
        }
    }

    public synchronized int incrementAndGet(UUIDPrefix prefix) {
        
        LocalDate now = getLocalDateNow(); 
        if ( ! now.isEqual(referenceDate) ) {
            synchronized (this) {
                if ( ! now.isEqual(referenceDate) ) {
                    LOG.info("incrementAndGet - reference date change detected, reseting counters. Current date={}, reference date={}", now, referenceDate);
                    resetAll(now);
                }
            }
        }
        
        int next = counters.computeIfAbsent( prefix, key -> new AtomicInteger( getPropertyValueAsInt(key.toString()) )).incrementAndGet();
        asynchSaveToStore();
        
        return next;
    }
    
    public synchronized String generateNextID(UUIDPrefix prefix) {
        int counter = incrementAndGet(prefix);
        return String.format("%s%s%s%06d", prefix.prefix, instanceUniqueId, referenceDate.format(localDateFormatter), counter);
    }

    private void resetAll(LocalDate newReferenceDate) {
        referenceDate = newReferenceDate;
        counters.clear();
        properties.clear();    
        synchSaveToStore();    
    }

    private int getPropertyValueAsInt(String key) {
        String value =  properties.getProperty(key, "0");
        
        if ( value.isEmpty() ) {
            value = "0";
        }
        
        return Integer.valueOf(value);
    }

    public void updateCounterForTestingOnly(UUIDPrefix prefix, int update) {
        counters.put(prefix, new AtomicInteger(update));
    }

}
