<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>um</artifactId>

  <parent>
    <groupId>ch.mks.wta4</groupId>
    <artifactId>wta4-fwk</artifactId>
    <version>53.0-SNAPSHOT</version>
  </parent>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-math3</artifactId>
      <version>3.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.mks.wta4</groupId>
      <artifactId>ita-lfx2-agg</artifactId>
      <version>53.0-SNAPSHOT</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-server</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hdrhistogram</groupId>
      <artifactId>HdrHistogram</artifactId>
      <version>2.2.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.mks.wta4</groupId>
      <artifactId>ita-simulator</artifactId>
      <version>53.0-SNAPSHOT</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.mks.wta4</groupId>
      <artifactId>ita-interfaces</artifactId>
      <version>53.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>ch.mks.wta4</groupId>
      <artifactId>ita-fix</artifactId>
      <version>53.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>ch.mks.wta4</groupId>
      <artifactId>findur-fix-client</artifactId>
      <version>53.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>it.sauronsoftware.cron4j</groupId>
      <artifactId>cron4j</artifactId>
      <version>2.2.5</version>
    </dependency>
    <dependency>
      <groupId>com.opencsv</groupId>
      <artifactId>opencsv</artifactId>
      <version>5.2</version>
    </dependency>
    <dependency>
      <groupId>com.mkspamp.eventbus</groupId>
      <artifactId>eventbus-client</artifactId>
      <version>1.0.4</version>
    </dependency>
  </dependencies>

</project>
