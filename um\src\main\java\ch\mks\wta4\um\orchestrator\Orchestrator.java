package ch.mks.wta4.um.orchestrator;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import ch.mks.wta4.common.service.AbstractWTA4Service;
import ch.mks.wta4.common.thread.ThreadUtils;
import ch.mks.wta4.configuration.IConfiguration;
import ch.mks.wta4.event.Event;
import ch.mks.wta4.event.EventFactory;
import ch.mks.wta4.event.EventType;
import ch.mks.wta4.event.HeartbeatMessage;
import ch.mks.wta4.event.IEventHandler;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;
import ch.mks.wta4.um.event.EventRouter;

public class Orchestrator extends AbstractWTA4Service implements IEventHandler, IOrchestrator {

    private final static Logger LOG = LoggerFactory.getLogger(Orchestrator.class);
    private String logInfo;
    private final EventRouter eventRouter;
    private final IConfiguration configuration;
    private final ScheduledExecutorService heartbeatExecutor;
    private final Cache<InstanceInfo, HeartbeatMessage> heartbeats;
    private final EventFactory eventFactory;
    private PrimaryStatus primaryStatus;
    private final CopyOnWriteArrayList<IOrchestratorPrimaryStatusListener> listeners = new CopyOnWriteArrayList<>();

    public Orchestrator(EventRouter eventRouter, IConfiguration configuration) {

        heartbeats = CacheBuilder.newBuilder().expireAfterWrite(configuration.getHeartbeatsTimeToLiveInCacheInSeconds(), TimeUnit.SECONDS).concurrencyLevel(1).build();
        this.eventRouter = eventRouter;
        this.configuration = configuration;
        this.heartbeatExecutor = Executors.newSingleThreadScheduledExecutor(ThreadUtils.getThreadFactory("wta4-orchestrator-heartbeat"));
        this.eventFactory = new EventFactory(configuration.getInstanceInfo());
        this.primaryStatus = PrimaryStatus.NOT_PRIMARY;

        logInfo = String.format("%s-%s", configuration.getInstanceInfo().getRegionId(), configuration.getInstanceInfo().getInstanceId());
    }

    @Override
    protected void startUp() throws Exception {
        LOG.info("startUp [{}]->", logInfo);

        if ( configuration.getEventBusEnabled() ) {
            eventRouter.registerHandler(EventType.INSTANCE_HEARTBEAT, this);
            eventRouter.registerHandler(EventType.REQUEST_FOR_HEARTBEATS, this);
            heartbeatExecutor.scheduleAtFixedRate(this::handleHeartbeat, configuration.getInstanceHeartbeatPeriodInSeconds(), configuration.getInstanceHeartbeatPeriodInSeconds(), TimeUnit.SECONDS);

            if (configuration.getPrimaryStatus() == PrimaryStatus.PRIMARY) {
                LOG.info("startUp [{}]- configured with primaryStatus=PRIMARY, attempting to acquire", logInfo);
                acquirePrimaryStatus();
            }

        } else {
            primaryStatus = PrimaryStatus.PRIMARY;
        }

        LOG.info("startUp [{}]<-", logInfo);
    }

    private void handleHeartbeat() {
        try {
            sendHeartbeat();
            checkCluster();
        } catch (Exception e) {
            LOG.error("handleHeartbeat [{}]- ", logInfo, e);
        }
    }

    private void checkCluster() {

        int primaryInstances = countPrimaryInstancesForThisRegion();

        if ( primaryInstances == 0 ) {

            if ( this.primaryStatus == PrimaryStatus.ACQUIRING_PRIMARY ) {
                LOG.warn("checkCluster [{}]- no PRIMARY instance found in cluster for this region, but this instance is ACQUIRING", logInfo);
            } else {
                LOG.error("checkCluster [{}]- no PRIMARY instance found in cluster for region {}, heartbeats={}", logInfo, configuration.getInstanceInfo().getRegionId(), getHeartbeats());
                listeners.forEach(listener -> {
                    try {
                        listener.onNoPrimaryFoundForRegion(configuration.getInstanceInfo(), getHeartbeats());
                    } catch (Exception e) {
                        LOG.error("checkCluster [{}]- to listener {}", listener, e);
                    }
                });
            }
        } else if ( primaryInstances > 1 ) {
            LOG.error("checkCluster [{}]- too many PRIMARY instance found in cluster for region {}. THIS IS REALLY BAD. IMMEDIATE ACTION REQUIRED: STOP ONE OF THE INSTANCES. heartbeats={}", logInfo, configuration.getInstanceInfo().getRegionId(), getHeartbeats());
            listeners.forEach(listener -> {
                try {
                    listener.onSplitBrainDetected(getHeartbeats());
                } catch (Exception e) {
                    LOG.error("checkCluster [{}]- to listener {}", logInfo, listener, e);
                }
            });
        }

    }

    private int countPrimaryInstancesForThisRegion() {
        String regionId = configuration.getInstanceInfo().getRegionId();
        int count = (int) heartbeats.asMap().values().stream().filter(hb -> regionId.equals(hb.getInstanceInfo().getRegionId()) && hb.getPrimaryStatus() == PrimaryStatus.PRIMARY).count();
        return count;
    }

    @Override
    public void sendHeartbeat() {
        HeartbeatMessage hb = new HeartbeatMessage(configuration.getInstanceInfo(), this.primaryStatus, ZonedDateTime.now());
        LOG.info("sendHeartbeat [{}]- {}", logInfo, hb);
        eventRouter.onEvent(eventFactory.create(EventType.INSTANCE_HEARTBEAT, configuration.getSystemUser().getUserId(), hb));
    }

    @Override
    public void requestForHeartbeats() {
        LOG.info("requestForHeartbeats [{}]", logInfo);
        eventRouter.onEvent(eventFactory.create(EventType.REQUEST_FOR_HEARTBEATS, configuration.getSystemUser().getUserId(), null));
    }

    @Override
    public PrimaryStatus getPrimaryStatus() {
        return primaryStatus;
    }

    @Override
    public Map<InstanceInfo, HeartbeatMessage> getHeartbeats() {
        return heartbeats.asMap();
    }

    @Override
    public PrimaryStatus acquirePrimaryStatus() {

        LOG.info("acquirePrimaryStatus [{}]-> ", logInfo);

        try {

            LOG.info("acquirePrimaryStatus [{}]- current primaryStatus={}, heartbeats={}", logInfo, primaryStatus, heartbeats.asMap());
            requestForHeartbeats();
            setPrimaryStatus(PrimaryStatus.ACQUIRING_PRIMARY);
            LOG.info("acquirePrimaryStatus [{}]- current primaryStatus={}, heartbeats={}, heartbeats requested", logInfo, primaryStatus, heartbeats.asMap());

            TimeUnit.SECONDS.sleep(configuration.getRequestForHearbeatsWaitSeconds());

            LOG.info("acquirePrimaryStatus [{}]- current primaryStatus={}, heartbeats={}, after heartbeats requested and waiting for {} seconds", logInfo, primaryStatus, heartbeats.asMap(), configuration.getRequestForHearbeatsWaitSeconds());

            HeartbeatMessage regionPrimaryInstanceHeartbeat = getRegionPrimaryInstanceHeartbeat();

            if (regionPrimaryInstanceHeartbeat != null) {
                LOG.warn("acquirePrimaryStatus [{}]- folding to NOT_PRIMARY as there is a primary instance for this region already, regionPrimaryInstanceHeartbeat={}", logInfo, regionPrimaryInstanceHeartbeat);
                setPrimaryStatus(PrimaryStatus.NOT_PRIMARY);
            } else {
                LOG.info("acquirePrimaryStatus [{}]- becoming PRIMARY as there is NO primary reported in the region", logInfo);
                setPrimaryStatus(PrimaryStatus.PRIMARY);
            }

        } catch (Exception e) {
            LOG.error("acquirePrimaryStatus [{}]- ", logInfo, e);
            throw new RuntimeException(e);
        }

        LOG.info("acquirePrimaryStatus [{}]<- returning primaryStatus={}", logInfo, primaryStatus);
        return primaryStatus;
    }

    private HeartbeatMessage getRegionPrimaryInstanceHeartbeat() {
        Optional<HeartbeatMessage> primaryHB = heartbeats.asMap().values().stream()
                .filter( hb ->
                    (! configuration.getInstanceInfo().isSameInstance(hb.getInstanceInfo())) // not myself
                    && configuration.getInstanceInfo().isSameRegion(hb.getInstanceInfo()) // AND same region
                    &&  ( hb.getPrimaryStatus() == PrimaryStatus.PRIMARY || hb.getPrimaryStatus() == PrimaryStatus.ACQUIRING_PRIMARY )) // AND (is primary or trying to become one)
                .findFirst();

        if (primaryHB.isPresent()) {
            return primaryHB.get();
        } else {
            return null;
        }
    }

    private void setPrimaryStatus(PrimaryStatus newPrimaryStatus) {

        if (this.primaryStatus == newPrimaryStatus) {
            LOG.warn("setPrimaryStatus [{}]- primaryStatus is already {}", logInfo, newPrimaryStatus);
            return;
        }

        LOG.info("setPrimaryStatus [{}]- setting primaryStatus to {} from {}", logInfo, newPrimaryStatus, this.primaryStatus);

        this.primaryStatus = newPrimaryStatus;
        sendHeartbeat();
        notifyPrimaryStatusChange();
    }

    private void notifyPrimaryStatusChange() {
        listeners.forEach(listener -> {
            try {

                listener.onPrimaryStatusUpdate(this.primaryStatus);

            } catch (Exception e) {
                LOG.error("notifyPrimaryStatusChange [{}]- to listener {}", logInfo, listener, e);
            }
        });
    }

    @Override
    public PrimaryStatus releasePrimaryStatus() {
        LOG.info("releasePrimaryStatus ->");

        try {
            LOG.info("releasePrimaryStatus [{}]- current primaryStatus={}, heartbeats={}", logInfo, primaryStatus, heartbeats.asMap());
            setPrimaryStatus(PrimaryStatus.RELEASING_PRIMARY);

        } catch (Exception e) {
            LOG.error("releasePrimaryStatus [{}]- ", logInfo, e);
            throw new RuntimeException(e);
        }

        LOG.info("releasePrimaryStatus [{}]<- returning primaryStatus={}", logInfo, primaryStatus);
        return primaryStatus;
    }

    @Override
    protected void shutDown() throws Exception {
        LOG.info("stop [{}]->", logInfo);

        if (primaryStatus == PrimaryStatus.PRIMARY) {
            releasePrimaryStatus();
        }

        heartbeatExecutor.shutdown();
        eventRouter.removeHandler(EventType.INSTANCE_HEARTBEAT, this);
        eventRouter.removeHandler(EventType.REQUEST_FOR_HEARTBEATS, this);
        LOG.info("stop [{}]<-", logInfo);
    }

    @Override
    public void handle(Event event) {

        LOG.info("handle [{}]- event={}", logInfo, event);

        switch (event.getType()) {

        case INSTANCE_HEARTBEAT:
            HeartbeatMessage hb = (HeartbeatMessage) event.getPayload();
            heartbeats.put(hb.getInstanceInfo(), hb);

            if (this.primaryStatus == PrimaryStatus.RELEASING_PRIMARY) {
                handleHearbeatWhileReleasing(hb);
            }
            break;

        case REQUEST_FOR_HEARTBEATS:
            LOG.info("handle [{}]- REQUEST_FOR_INSTANCE_HEARTBEAT from instance={}", logInfo, event.getInstanceInfo());
            sendHeartbeat();
            break;

        default:
            LOG.warn("handle [{}]- no handler defined for event={}", logInfo, event);

        }

    }

    private void handleHearbeatWhileReleasing(HeartbeatMessage hb) {
        if (this.primaryStatus == PrimaryStatus.RELEASING_PRIMARY && hb.getPrimaryStatus() == PrimaryStatus.PRIMARY
                && configuration.getInstanceInfo().isSameRegion(hb.getInstanceInfo())) {

            LOG.info("handleHearBeatWhileReleasing [{}]- this.primaryStatus={}, instance {} primaryStatus={}, setting this instance to NOT_PRIMARY. event={}", logInfo, this.primaryStatus, hb.getInstanceInfo(), hb.getPrimaryStatus());
            setPrimaryStatus(PrimaryStatus.NOT_PRIMARY);
        }

    }

    @Override
    public void addListener(IOrchestratorPrimaryStatusListener listener) {
        listeners.add(listener);
    }

    @Override
    public void removeListener(IOrchestratorPrimaryStatusListener listener) {
        listeners.remove(listener);
    }

    @Override
    public boolean isPrimary() {
        return PrimaryStatus.PRIMARY == this.primaryStatus;
    }


	@Override
	public InstanceInfo getPrimaryInstance() {
		if (this.getPrimaryStatus() == PrimaryStatus.PRIMARY) {
			return this.configuration.getInstanceInfo();
		}

		Optional<HeartbeatMessage> primaryHeartBeat = this.getHeartbeats().values().stream()
				.filter(hb -> configuration.getInstanceInfo().isSameRegion(hb.getInstanceInfo())
						&& hb.getPrimaryStatus() == PrimaryStatus.PRIMARY)
				.findFirst();

		return primaryHeartBeat.map(hb -> hb.getInstanceInfo()).orElseThrow();
	}

    @Override
    public Collection<InstanceInfo> getAllInstances() {
        if (this.configuration.getEventBusEnabled()) {
            return this.heartbeats.asMap().keySet();
        } else {
            return List.of(this.configuration.getInstanceInfo());
        }
    }

}
