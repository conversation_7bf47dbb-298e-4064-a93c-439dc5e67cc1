package com.mkspamp.eventbus.serialization;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.mkspamp.eventbus.model.definitions.Time;

@SuppressWarnings("serial")
public class TimeSerializer extends StdSerializer<Time> {

    public TimeSerializer() {
        this(null);
    }

    public TimeSerializer(final Class<Time> vc) {
        super(vc);
    }

    @Override
    public void serialize(Time value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        if ( value.getTime() != null ) gen.writeStringField("time", value.getTime().toString());
        if ( value.getZoneId() != null ) gen.writeStringField("zoneId", value.getZoneId().toString());
        gen.writeEndObject();
    }

}
