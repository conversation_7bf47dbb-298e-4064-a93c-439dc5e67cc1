<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>ch.mks.wta4</groupId>
		<artifactId>wta4-fwk</artifactId>
		<version>53.0-SNAPSHOT</version>
	</parent>
	<artifactId>services</artifactId>
	<name>services</name>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>ita-interfaces</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>um</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.firebase</groupId>
			<artifactId>firebase-admin</artifactId>
			<version>9.0.0</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-storage</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-firestore</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20210307</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.10.3</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>jwks-rsa</artifactId>
			<version>0.20.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mock-server</groupId>
			<artifactId>mockserver-netty</artifactId>
			<version>5.10</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mock-server</groupId>
			<artifactId>mockserver-client-java</artifactId>
			<version>5.10</version>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
  		 	<groupId>com.google.api-client</groupId>
   		  	<artifactId>google-api-client-jackson2</artifactId>
    		<version>1.20.0</version>
		</dependency>

	</dependencies>
</project>
