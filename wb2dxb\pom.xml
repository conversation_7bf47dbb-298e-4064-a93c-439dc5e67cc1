<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>wb2dxb</artifactId>
	<packaging>jar</packaging>

	<name>wb2dxb</name>

	<parent>
		<groupId>ch.mks.wta4</groupId>
		<artifactId>wta4-fwk</artifactId>
		<version>53.0-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>wta4-common-client</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
						<configuration>
							<mainClass>
								ch.mks.wta4.wb2dxb.config.Application
							</mainClass>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
