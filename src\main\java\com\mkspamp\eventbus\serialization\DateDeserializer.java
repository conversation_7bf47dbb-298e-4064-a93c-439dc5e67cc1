package com.mkspamp.eventbus.serialization;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.mkspamp.eventbus.model.definitions.Date;

@SuppressWarnings("serial")
public class DateDeserializer extends StdDeserializer<Date> {

    public DateDeserializer() {
        this(null);
    }

    public DateDeserializer(final Class<Date> vc) {
        super(vc);
    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode tradeNode = mapper.readTree(jsonParser);      
        
        Date res = new Date();
        
        if ( tradeNode.has("date")) {
            res = res.withDate(LocalDate.parse(tradeNode.get("date").asText()));
        }
        
        if ( tradeNode.has("zoneId")) {
            res = res.withZoneId(ZoneId.of(tradeNode.get("zoneId").asText()));
        }
        
        return res;
    }

}
