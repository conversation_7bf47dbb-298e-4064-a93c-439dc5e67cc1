package com.mkspamp.eventbus.serialization;

import java.io.IOException;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.OptionExercisedEvent;
import com.mkspamp.eventbus.model.OptionExpiredEvent;
import com.mkspamp.eventbus.model.TradeCanceledEvent;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.TradeUpdatedEvent;
import com.mkspamp.eventbus.model.definitions.EventType;

@SuppressWarnings("serial")
public class EventDeserializer extends StdDeserializer<Event> {

    public EventDeserializer() {
        this(null);
    }

    public EventDeserializer(final Class<?> vc) {
        super(vc);
    }

    @Override
    public Event deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {

        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode eventNode = mapper.readTree(jsonParser);
        EventType eventType = EventType.fromValue(eventNode.get("type").asText());

        switch (eventType) {
        case TRADE_CREATED:
            return mapper.treeToValue(eventNode, TradeCreatedEvent.class);
        case TRADE_CANCELED:
            return mapper.treeToValue(eventNode, TradeCanceledEvent.class);
        case TRADE_UPDATED:
            return mapper.treeToValue(eventNode, TradeUpdatedEvent.class);
        case OPTION_EXERCISED:
            return mapper.treeToValue(eventNode, OptionExercisedEvent.class);
        case OPTION_EXPIRED:
            return mapper.treeToValue(eventNode, OptionExpiredEvent.class);
        default:
            throw new RuntimeException("Unexpected event type : " + eventType);
        }
    }

}
