package com.mkspamp.eventbus.serialization;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.mkspamp.eventbus.model.definitions.Date;

@SuppressWarnings("serial")
public class DateSerializer extends StdSerializer<Date> {

    public DateSerializer() {
        this(null);
    }

    public DateSerializer(final Class<Date> vc) {
        super(vc);
    }

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        if ( value.getDate() != null ) gen.writeStringField("date", value.getDate().toString());
        if ( value.getZoneId() != null ) gen.writeStringField("zoneId", value.getZoneId().toString());
        gen.writeEndObject();
    }

}
