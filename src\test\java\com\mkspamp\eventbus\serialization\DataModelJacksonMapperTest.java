package com.mkspamp.eventbus.serialization;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Set;

import javax.validation.ConstraintViolation;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.json.JsonMapper;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.FindurProperties;
import com.mkspamp.eventbus.model.ForwardTrade;
import com.mkspamp.eventbus.model.OptionExercisedEvent;
import com.mkspamp.eventbus.model.OptionTrade;
import com.mkspamp.eventbus.model.SpotTrade;
import com.mkspamp.eventbus.model.SrcAdditionalProperties;
import com.mkspamp.eventbus.model.SwapTrade;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.TradeCreatedEvent;
import com.mkspamp.eventbus.model.definitions.Date;
import com.mkspamp.eventbus.model.definitions.EventType;
import com.mkspamp.eventbus.model.definitions.Operation;
import com.mkspamp.eventbus.model.definitions.Price;
import com.mkspamp.eventbus.model.definitions.Quantity;
import com.mkspamp.eventbus.model.definitions.Time;
import com.mkspamp.eventbus.model.definitions.TradeType;

class DataModelJacksonMapperTest {
    
    
    
    TradeType type = TradeType.SPOT;
    String id = "DWTA43534534252345345";
    String src = "WTA";
    String traderId = "<EMAIL>";
    String counterpartyId = "ABOSSO BU";
    ZonedDateTime dateTime = ZonedDateTime.now();
    String bookingId = "645645";
    String portfolio = "Futures.GVA";
    String swapId = "sdfas";
    String modelVersion = "0.0.1";
    
    Operation operation = Operation.BUY;
    BigDecimal price = BigDecimal.valueOf(3000d);
    private JsonMapper jm;

    @BeforeEach
    void setUp() throws Exception {
        jm = new DataModelJacksonMapperFactory().create(true);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testTrade() throws Exception {
        
        FindurProperties findur = new FindurProperties().withBookingId(bookingId).withPortfolio(portfolio);
        
        Trade trade = new Trade()
                .withType(type)
                .withId(id)
                .withSrc(src)
                .withTraderId(traderId)
                .withCounterpartyId(counterpartyId)
                .withTradeDatetime( dateTime)          
                .withSrcAdditionalProperties(new SrcAdditionalProperties().withFindurProperties(findur))
                .withParentSwapId(swapId)
                ;
        
        String json = jm.writeValueAsString(trade);
        
        System.err.println(json);
        
        Trade dTrade = jm.readValue(json, Trade.class);
        
        assertEquals(trade, dTrade);
        
        assertEquals(dateTime, dTrade.getTradeDatetime());
    
    }
    @Test
    void testSpotTrade() throws Exception {
        
        SpotTrade trade = new SpotTrade()
                .withOperation(operation)
                .withPrice(new Price().withAmount(price))
                .withSettlementDate(new Date().withDate(LocalDate.now()).withZoneId(ZoneId.systemDefault()));
        ;
        String json = jm.writeValueAsString(trade);
        
        System.err.println(json);
        
        SpotTrade dTrade = jm.readValue(json, SpotTrade.class);
        assertEquals(trade, dTrade);
    }
    
    @Test
    void testDatesWithoutZoneId() throws Exception{
        String json; 
        Date dateSrc,dateDest;
        
        dateSrc = new Date();
        json = jm.writeValueAsString(dateSrc);
        dateDest = jm.readValue(json, Date.class);
        assertEquals(dateSrc, dateDest);
        
        dateSrc = new Date().withDate(LocalDate.now());
        json = jm.writeValueAsString(dateSrc);
        dateDest = jm.readValue(json, Date.class);
        assertEquals(dateSrc, dateDest);
        
        dateSrc = new Date().withDate(LocalDate.now()).withZoneId(ZoneId.systemDefault());
        json = jm.writeValueAsString(dateSrc);
        dateDest = jm.readValue(json, Date.class);
        assertEquals(dateSrc, dateDest);
    }
    
    @Test
    void testTimeSerializer() throws Exception {
        
        Time timeSrc, timeDest;
        String json;
        
        timeSrc = new Time().withTime(LocalTime.now()).withZoneId(ZoneId.systemDefault());
        json = jm.writeValueAsString(timeSrc);
        timeDest = jm.readValue(json, Time.class);
        assertEquals(timeSrc, timeDest);
        
        timeSrc = new Time().withTime(LocalTime.now());
        json = jm.writeValueAsString(timeSrc);
        timeDest = jm.readValue(json, Time.class);
        assertEquals(timeSrc, timeDest);
        
    }
    
    @Test
    void testSpotSwap() throws Exception {
        
        SwapTrade trade = new SwapTrade()
                .withType(TradeType.SWAP)
                .withSwapId(swapId)
                .withNearLeg(new SpotTrade().withType(TradeType.SPOT).withId(id).withPrice(new Price().withAmount(price)))
                .withFarLeg(new ForwardTrade().withType(TradeType.FORWARD).withId(id).withForwardPrice(new Price().withAmount(price)));
        ;
        String json = jm.writeValueAsString(trade);
        
        System.err.println(json);
        
        SwapTrade dTrade = (SwapTrade)jm.readValue(json, Trade.class);
        assertEquals(trade.getNearLeg().getId(), dTrade.getNearLeg().getId());
        assertEquals(trade.getFarLeg().getId(), dTrade.getFarLeg().getId());
        assertEquals(trade, dTrade);
    }
    
    @Test
    void testPolymorphicDeserializer() throws Exception {
        
        SpotTrade trade = new SpotTrade()
                .withType(TradeType.SPOT)
                .withOperation(operation)
                .withPrice(new Price().withAmount(price));
        ;
        
        String json = jm.writeValueAsString(trade);
        
        System.err.println(json);
        
        Trade dTrade = jm.readValue(json, Trade.class);
        assertEquals(trade, dTrade);
    }
    
    @Test
    public void testEvent() throws Exception {
        Event e = new TradeCreatedEvent()
                        .withType(EventType.TRADE_CREATED)
                        .withPayload(
                                new SpotTrade()
                                    .withType(TradeType.SPOT)
                                    .withPrice(
                                            new Price().withAmount(price))
                                    );
        String json = jm.writeValueAsString(e);
        
        System.err.println(json);
        
        TradeCreatedEvent de = (TradeCreatedEvent) jm.readValue(json, Event.class);
        assertEquals(e, de);
        
        System.err.println(de);
    }
    
    @Test
    public void testOptionExercisedEvent() throws Exception {
        Event e = new OptionExercisedEvent()
                        .withType(EventType.OPTION_EXERCISED)
                        .withPayload(new OptionTrade().withType(TradeType.OPTION).withStrike(new Price().withAmount(price)));
        String json = jm.writeValueAsString(e);
        
        System.err.println(json);
        
        OptionExercisedEvent de = (OptionExercisedEvent) jm.readValue(json, Event.class);
        assertEquals(e, de);
        
        System.err.println(de);
    }
    
    @Test
    public void testValidation() throws Exception {
        Event e = new TradeCreatedEvent()
                .withType(EventType.TRADE_CREATED)
                .withPayload(
                        new SpotTrade()
                            .withType(TradeType.SPOT)
                            .withPrice( new Price().withAmount(price.negate())).withQuantity(new Quantity().withAmount(BigDecimal.ZERO)) );
        
        DataModelValidator validator = new DataModelValidator();
        Set<ConstraintViolation<Event>> validationResults = validator.validate(e);
        validationResults.stream().map(cv -> String.format("%s - property %s - %s", cv.getRootBeanClass().getSimpleName(), cv.getPropertyPath(), cv.getMessage())).forEach(System.err::println);
        assertTrue(validationResults.size() > 0);
        assertTrue(validationResults.stream().map(ConstraintViolation::getMessage).anyMatch(s -> s.equals("must be greater than or equal to 0")));
        assertTrue(validationResults.stream().map(ConstraintViolation::getMessage).anyMatch(s -> s.equals("must be greater than or equal to 1")));
        assertTrue(validationResults.stream().map(ConstraintViolation::getMessage).anyMatch(s -> s.equals("must not be null")));
    }
}
