package com.mkspamp.eventbus.serialization;

import java.io.IOException;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.mkspamp.eventbus.model.CashTrade;
import com.mkspamp.eventbus.model.ForwardTrade;
import com.mkspamp.eventbus.model.FutureTrade;
import com.mkspamp.eventbus.model.OptionTrade;
import com.mkspamp.eventbus.model.SpotTrade;
import com.mkspamp.eventbus.model.SwapTrade;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.definitions.TradeType;

@SuppressWarnings("serial")
public class TradeDeserializer extends StdDeserializer<Trade> {

    public TradeDeserializer() {
        this(null);
    }

    public TradeDeserializer(final Class<?> vc) {
        super(vc);
    }

    @Override
    public Trade deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {

        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode tradeNode = mapper.readTree(jsonParser);
        TradeType tradeType = TradeType.fromValue(tradeNode.get("type").asText());

        switch (tradeType) {
        case SPOT:
            return mapper.treeToValue(tradeNode, SpotTrade.class);
        case FUTURE:
            return mapper.treeToValue(tradeNode, FutureTrade.class);
        case FORWARD:
            return mapper.treeToValue(tradeNode, ForwardTrade.class);
        case CASH:
            return mapper.treeToValue(tradeNode, CashTrade.class);
        case OPTION:
            return mapper.treeToValue(tradeNode, OptionTrade.class);
        case SWAP:
            return mapper.treeToValue(tradeNode, SwapTrade.class);
        default:
            throw new RuntimeException("Unexpected trade type : " + tradeType);
        }
    }

}
