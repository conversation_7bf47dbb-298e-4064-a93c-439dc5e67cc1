<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>ch.mks.wta4</groupId>
  <artifactId>wta4-fwk</artifactId>
  <version>53.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <properties>
    <spring-boot.version>3.3.4</spring-boot.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>17</java.version>
  </properties>

  <scm>
    <developerConnection>scm:git:ssh://*****************/mkspamp/wta4.git</developerConnection>
    <tag>wta4-fwk-36.5</tag>
  </scm>

  <modules>
    <module>common</module>
    <module>ita-interfaces</module>
    <module>services</module>
    <module>findur-fix-client</module>
    <module>um</module>
    <module>ita-lfx2-agg</module>
    <module>ita-fix</module>
    <module>ita-simulator</module>
    <module>wta4-common-client</module>
    <module>wta4</module>
    <module>wb2dxb</module>
    <module>wb2hkg</module>
    <module>um-grpc-adapter</module>
    <module>um-grpc-client</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>33.3.0-jre</version>
      </dependency>
      <dependency>
        <groupId>org.quickfixj</groupId>
        <artifactId>quickfixj-core</artifactId>
        <version>2.3.1</version>
	  </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>3.12.4</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>2.17.2</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>
  </dependencies>

  <distributionManagement>
	<repository>
			<id>mkspampgroup</id>
			<url>https://pkgs.dev.azure.com/mkspampgroup/_packaging/mkspampgroup/maven/v1</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
  </distributionManagement>

  <repositories>
    <repository>
			<id>mkspampgroup</id>
			<url>https://pkgs.dev.azure.com/mkspampgroup/_packaging/mkspampgroup/maven/v1</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
  </repositories>

  <build>
    <plugins>
    
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <compilerArgs>
            <arg>-parameters</arg> <!-- Ensure parameter names are retained -->
          </compilerArgs>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <version>10.0.4</version>
      </plugin>

    </plugins>
  </build>

</project>
