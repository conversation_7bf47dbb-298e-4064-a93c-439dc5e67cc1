<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mkspamp.eventbus</groupId>
  <artifactId>eventbus-tools</artifactId>
  <version>1.0.1-SNAPSHOT</version>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <jackson.version>2.17.1</jackson.version>
    <junit.jupyter.version>5.11.3</junit.jupyter.version>
  </properties>
  
  <scm>
    <developerConnection>scm:git:ssh://*****************/mkspamp/eventbus-tools.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <distributionManagement>
    <repository>
      <id>mkspampgroup</id>
      <url>
        https://pkgs.dev.azure.com/mkspampgroup/_packaging/mkspampgroup/maven/v1</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>mkspampgroup</id>
      <url>
        https://pkgs.dev.azure.com/mkspampgroup/_packaging/mkspampgroup/maven/v1</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>
  
  <dependencies>
    
    <dependency>
      <groupId>com.mkspamp.eventbus</groupId>
      <artifactId>eventbus-model</artifactId>
      <version>0.0.7</version>
    </dependency>
    
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>2.0.1.Final</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>6.2.5.Final</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>javax.el</groupId>
      <artifactId>javax.el-api</artifactId>
      <version>3.0.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.glassfish.web</groupId>
      <artifactId>javax.el</artifactId>
      <version>2.2.6</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit.jupyter.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
    <build>
    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>3.1.1</version>
      </plugin>

    </plugins>
  </build>
</project>