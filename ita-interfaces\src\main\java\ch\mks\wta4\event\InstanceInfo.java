package ch.mks.wta4.event;

import java.io.Serializable;
import java.util.Objects;

public class InstanceInfo implements Serializable{

    private static final long serialVersionUID = 1L;
    private final String appId;
    private final String regionId;
    private final String instanceId;
    private final String uniqueId;
    
    public InstanceInfo(String appId, String regionId, String instanceId) {
        this.appId = appId;
        this.regionId = regionId;
        this.instanceId = instanceId;
        uniqueId = prepareUniqueId();
    }
    
    public String getAppId() {
        return appId;
    }
    public String getRegionId() {
        return regionId;
    }
    public String getInstanceId() {
        return instanceId;
    }
    
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("InstanceInfo [appId=").append(appId).append(", regionId=").append(regionId).append(", instanceId=").append(instanceId).append("]");
        return builder.toString();
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, instanceId, regionId);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        InstanceInfo other = (InstanceInfo) obj;
        return Objects.equals(appId, other.appId) && Objects.equals(instanceId, other.instanceId) && Objects.equals(regionId, other.regionId);
    }
    
    public boolean isSameRegion(InstanceInfo instanceInfo) {
    	return Objects.equals(regionId, instanceInfo.regionId);
    }
    
    public boolean isSameInstance(InstanceInfo instanceInfo) {
    	if ( instanceInfo == null ) return false;
    	return this.equals(instanceInfo);
    }
    
    public String getUniqueId() {
    	return uniqueId;
    }
    
    private String prepareUniqueId() {
    	StringBuilder sb = new StringBuilder();
        if (appId != null && !appId.isEmpty()) sb.append(appId);
        if (regionId != null && !regionId.isEmpty()) sb.append(regionId);
        if (instanceId != null && !instanceId.isEmpty()) sb.append(instanceId);
        return sb.toString();
    }
    
}
