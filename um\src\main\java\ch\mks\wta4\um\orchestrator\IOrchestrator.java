package ch.mks.wta4.um.orchestrator;

import java.util.Collection;
import java.util.Map;
import java.util.NoSuchElementException;

import ch.mks.wta4.event.HeartbeatMessage;
import ch.mks.wta4.event.InstanceInfo;
import ch.mks.wta4.event.PrimaryStatus;

public interface IOrchestrator {

	public PrimaryStatus getPrimaryStatus();
	public Map<InstanceInfo, HeartbeatMessage> getHeartbeats();
	public void sendHeartbeat();
	public void requestForHeartbeats();
	public PrimaryStatus acquirePrimaryStatus();
	public PrimaryStatus releasePrimaryStatus();
	public boolean isPrimary();

	/**
	 * Gets the primary instance of the orchestrated system.
	 * @return the {@link InstanceInfo} of the primary instance, or a {@link NoSuchElementException} if no instance is defined.
	 */
	public InstanceInfo getPrimaryInstance();

	Collection<InstanceInfo> getAllInstances();

	public void addListener(IOrchestratorPrimaryStatusListener listener);
	public void removeListener(IOrchestratorPrimaryStatusListener listener);

    public interface IOrchestratorPrimaryStatusListener{
		public void onPrimaryStatusUpdate(PrimaryStatus primaryStatus);
		public void onNoPrimaryFoundForRegion(InstanceInfo instance, Map<InstanceInfo, HeartbeatMessage> heatbeats);
		public void onSplitBrainDetected(Map<InstanceInfo, HeartbeatMessage> heatbeats);
	}

}
