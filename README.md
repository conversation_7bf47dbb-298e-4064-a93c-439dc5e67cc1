# Release process
Project uses Maven release plugin. The release takes two steps: prepare and perform.

Commands below are to be executed from the parent project folder and having a clean copy of the branch to be release checked out and fully synchronized with the repository (no local changes nor pending pull).

Branch to be released is "master" for a normal release or the branch with the hot fix for a hot fix (see Hot fix process)

See https://maven.apache.org/maven-release/maven-release-plugin/ for more opptions (e.g., rollback if something goes wrong)

## Prepare

Actions performed:

  * Creates a tag in git with the release source, with all POMs updated to the release version
  * Updates current branch with the next development version
  * Creates release.properties file to be used by Perform step to complete the release

The information required to perform the actions is interactively requested by Maven.

Command: 

`mvn release:prepare`

The project convention is to accept the plugin proposed values:

  * remove SNAPSHOT from current version to get the release version
  * increase the SNAPSHOT second number for the development version
  * take ate-[release version] as the tag name

## Perform

Executed from the same folder where Prepare has been executed (uses release.properties)

Actions performed:

  * checkout the release branch
  * call mvn deploy (copies to nexus repository)

`mvn release:perform` 

## Clean

The plugin creates several temporary files to manage the release. These can be automatically cleaned up with:

`mvn release:clean`

## Hot fix process

  * Create a branch from the release tag to fix
    * `git checkout -b [branch name] [tag name]`
    * `e.g.: git checkout -b hotfix/cpfs2-2.10 cpfs2-2.10`

  * Change the pom version in the branch, when prompted enter the development version for the hotfix (e.g. 2.10.1-SNAPSHOT)
    * `mvn release:update-versions`

  * Perform modification to the branch to implement the hotfix
  * Commit changes to the new branch
  * Refer to the release process to release the hot fix