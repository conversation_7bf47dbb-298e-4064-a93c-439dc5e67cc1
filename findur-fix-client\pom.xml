<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>findur-fix-client</artifactId>

 	<parent>
    	<groupId>ch.mks.wta4</groupId>
    	<artifactId>wta4-fwk</artifactId>
    	<version>53.0-SNAPSHOT</version>
  	</parent>

	<dependencies>
		<dependency>
			<groupId>org.quickfixj</groupId>
			<artifactId>quickfixj-core</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>common</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
</project>