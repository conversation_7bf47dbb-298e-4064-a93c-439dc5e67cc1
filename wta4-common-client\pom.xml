<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>wta4-common-client</artifactId>
	<packaging>jar</packaging>
	<name>wta4-common-client</name>

	<parent>
		<groupId>ch.mks.wta4</groupId>
		<artifactId>wta4-fwk</artifactId>
		<version>53.0-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
		</dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>um</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>services</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>ita-interfaces</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>ita-fix</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>ita-lfx2-agg</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>ita-simulator</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>um-grpc-adapter</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>

	</dependencies>

</project>