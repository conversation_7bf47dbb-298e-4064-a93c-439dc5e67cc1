package com.mkspamp.eventbus.serialization;

import java.time.ZonedDateTime;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.mkspamp.eventbus.model.Event;
import com.mkspamp.eventbus.model.Trade;
import com.mkspamp.eventbus.model.definitions.Date;
import com.mkspamp.eventbus.model.definitions.Time;

public class DataModelJacksonMapperFactory{

    public JsonMapper create(boolean prettyprintjson) {
        JsonMapper mapper = JsonMapper.builder()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(SerializationFeature.INDENT_OUTPUT, prettyprintjson)
                .serializationInclusion(Include.NON_NULL)
                .build();

        SimpleModule module = new SimpleModule()
                                    .addDeserializer(Trade.class, new TradeDeserializer())
                                    .addDeserializer(Event.class, new EventDeserializer())
                                    .addDeserializer(ZonedDateTime.class, new ZonedDateTimeDeserializer())
                                    .addDeserializer(Date.class, new DateDeserializer())
                                    .addDeserializer(Time.class, new TimeDeserializer())
                                    .addSerializer(ZonedDateTime.class, new ZonedDateTimeSerializer())
                                    .addSerializer(Date.class, new DateSerializer())
                                    .addSerializer(Time.class, new TimeSerializer());
                                    

        mapper.registerModule(module);
        return mapper;
    }

}
