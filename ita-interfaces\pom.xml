<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>ita-interfaces</artifactId>

	<parent>
		<groupId>ch.mks.wta4</groupId>
		<artifactId>wta4-fwk</artifactId>
		<version>53.0-SNAPSHOT</version>
	</parent>
	
	<dependencies>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-broker</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-kahadb-store</artifactId>
		</dependency>
		<dependency>
        	<groupId>org.apache.activemq</groupId>
        	<artifactId>activemq-stomp</artifactId>
    	</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.mkspamp.eventbus</groupId>
			<artifactId>eventbus-model</artifactId>
			<version>0.0.2</version>
		</dependency>
		<dependency>
			<groupId>ch.mks.wta4</groupId>
			<artifactId>common</artifactId>
			<version>53.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
</project>