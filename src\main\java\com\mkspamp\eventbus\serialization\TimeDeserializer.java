package com.mkspamp.eventbus.serialization;

import java.io.IOException;
import java.time.LocalTime;
import java.time.ZoneId;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.mkspamp.eventbus.model.definitions.Time;

@SuppressWarnings("serial")
public class TimeDeserializer extends StdDeserializer<Time> {

    public TimeDeserializer() {
        this(null);
    }

    public TimeDeserializer(final Class<Time> vc) {
        super(vc);
    }

    @Override
    public Time deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode tradeNode = mapper.readTree(jsonParser);      
        
        Time res = new Time();
        
        if ( tradeNode.has("time")) {
            res = res.withTime(LocalTime.parse(tradeNode.get("time").asText()));
        }
        
        if ( tradeNode.has("zoneId")) {
            res = res.withZoneId(ZoneId.of(tradeNode.get("zoneId").asText()));
        }
        
        return res;
    }

}
