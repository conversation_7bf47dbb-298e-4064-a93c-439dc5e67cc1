Stack trace:
Frame         Function      Args
0007FFFF9C30  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8B30) msys-2.0.dll+0x2116E
0007FFFF9C30  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9C30  0002100469F2 (00021028DF99, 0007FFFF9AE8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9C30  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF9C30  00021006A525 (0007FFFF9C40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF9C40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC7BAF0000 ntdll.dll
7FFC79F60000 KERNEL32.DLL
7FFC79320000 KERNELBASE.dll
7FFC7B490000 USER32.dll
7FFC78F20000 win32u.dll
7FFC7A190000 GDI32.dll
7FFC78BE0000 gdi32full.dll
7FFC78D10000 msvcp_win.dll
7FFC78F50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC7AA80000 advapi32.dll
7FFC79C80000 msvcrt.dll
7FFC79D60000 sechost.dll
7FFC78EF0000 bcrypt.dll
7FFC7B650000 RPCRT4.dll
7FFC78290000 CRYPTBASE.DLL
7FFC79700000 bcryptPrimitives.dll
7FFC79F20000 IMM32.DLL
